2025-07-09 18:07:18.911 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-09 18:07:18.911 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-09 18:07:18.911 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false}
2025-07-09 18:07:18.911 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-09 18:07:18.911 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-09 18:07:18.911 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-09 18:07:21.112 [info] 'OAuthFlow' Creating new session...
2025-07-09 18:07:21.113 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=RcOaYQjqj8623QbCxIc4TtWKLqJ10GCmc7Y0oS3NJT4&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode-insiders%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=ae95b09a-06e8-4b00-9834-fc22181ab51d&scope=email&prompt=login
2025-07-09 18:07:54.037 [error] 'AugmentExtension' API request dc88f848-5dae-4ff0-baee-2f38e970776c to https://d11.api.augmentcode.com/token failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:07:54.041 [error] 'AugmentExtension' Dropping error report "token call failed with APIStatus unavailable" due to error: Please configure Augment API URL
2025-07-09 18:07:54.041 [error] 'OAuthFlow' Failed to get and save access token: fetch failed
2025-07-09 18:07:54.042 [warning] 'OAuthFlow' Failed to process auth request: If you have a firewall, please add "https://d11.api.augmentcode.com/" to your allowlist.
2025-07-09 18:08:25.392 [info] 'OAuthFlow' Creating new session...
2025-07-09 18:08:25.396 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=q-H_HCBdg87DooqQBUxZKTwTU55l8fBWYWcBRtTV7jQ&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode-insiders%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=10b0b436-466d-46ac-8556-c5ffe349edb3&scope=email&prompt=login
2025-07-09 18:08:47.092 [info] 'activate()' ======== Reloading extension ========
2025-07-09 18:08:47.093 [info] 'AugmentExtension' Retrieving model config
2025-07-09 18:08:47.093 [info] 'OAuthFlow' Created session https://d11.api.augmentcode.com/
2025-07-09 18:08:47.763 [info] 'AugmentExtension' Retrieved model config
2025-07-09 18:08:47.763 [info] 'AugmentExtension' Returning model config
2025-07-09 18:08:47.775 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
  - retryChatStreamTimeouts: false to true
2025-07-09 18:08:47.776 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-07-09 18:08:47.776 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [false]
2025-07-09 18:08:47.776 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-09 18:08:47.855 [info] 'WorkspaceManager' Workspace startup complete in 8 ms
2025-07-09 18:08:47.855 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-09 18:08:47.855 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-09 18:08:47.855 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-09 18:08:47.857 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-07-09 18:08:48.354 [error] 'AugmentExtension' API request 318ed792-f121-46cf-9f0a-2b6892a382af to https://d11.api.augmentcode.com/agents/list-remote-tools response 402: Payment Required
2025-07-09 18:08:48.864 [error] 'AugmentExtension' API request 93555f22-9b83-4261-be2b-10326505610a to https://d11.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:08:48.864 [error] 'AugmentExtension' Dropping error report "agents/list-remote-tools call failed with APIStatus unknown" due to error: This operation was aborted
2025-07-09 18:08:48.865 [error] 'VSCodeRemoteInfo' Failed to list remote tools HTTP error: 402 Payment Required
2025-07-09 18:08:48.976 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-07-09 18:08:58.292 [error] 'AugmentExtension' API request a705b1dc-d7c4-48e9-a506-4bb3656e80bb to https://d11.api.augmentcode.com/record-session-events response 402: Payment Required
2025-07-09 18:08:58.293 [error] 'AugmentExtension' API request 82dba438-e32e-436e-84f5-70bcdc7f7881 to https://d11.api.augmentcode.com/record-session-events response 402: Payment Required
2025-07-09 18:08:58.362 [error] 'AugmentExtension' API request e509dd4a-7cba-4951-9b44-a65ee39e56c8 to https://d11.api.augmentcode.com/client-metrics response 402: Payment Required
2025-07-09 18:08:58.441 [error] 'AugmentExtension' API request b74b304e-ba0f-4b18-ad71-0e222c87821e to https://d11.api.augmentcode.com/report-feature-vector response 402: Payment Required
2025-07-09 18:08:58.465 [error] 'AugmentExtension' API request 9b3dad28-6f43-4cde-88b5-ffe3fdf4f39e to https://d11.api.augmentcode.com/record-session-events response 402: Payment Required
2025-07-09 18:08:58.795 [error] 'AugmentExtension' API request 6c836428-0965-45cf-a09e-0162b10db003 to https://d11.api.augmentcode.com/report-error response 402: Payment Required
2025-07-09 18:08:58.795 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus unknown" due to error: HTTP error: 402 Payment Required
2025-07-09 18:08:58.796 [error] 'OnboardingSessionEventReporter' Error uploading metrics: Error: HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logOnboardingSessionEvent (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:33690)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.797 [error] 'OnboardingSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logOnboardingSessionEvent (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:33690)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.800 [error] 'AugmentExtension' API request 627b0747-f452-4343-9fb9-9035cfde2ae5 to https://d11.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:08:58.800 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus unknown" due to error: This operation was aborted
2025-07-09 18:08:58.800 [error] 'NextEditSessionEventReporter' Error uploading metrics: Error: HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logNextEditSessionEvent (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:33327)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.800 [error] 'NextEditSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logNextEditSessionEvent (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:33327)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.905 [error] 'AugmentExtension' API request bd42f15b-ba4a-420b-b353-70ef928d832b to https://d11.api.augmentcode.com/report-error response 402: Payment Required
2025-07-09 18:08:58.905 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unknown" due to error: HTTP error: 402 Payment Required
2025-07-09 18:08:58.905 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.905 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.913 [error] 'AugmentExtension' API request 466a8786-8641-4dfc-b2c7-5bf307e46ecc to https://d11.api.augmentcode.com/report-error response 402: Payment Required
2025-07-09 18:08:58.913 [error] 'AugmentExtension' Dropping error report "report-feature-vector call failed with APIStatus unknown" due to error: HTTP error: 402 Payment Required
2025-07-09 18:08:58.913 [error] 'FeatureVectorReporter' Error uploading metrics: Error: HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logFeatureVector (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:33123)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.913 [error] 'FeatureVectorReporter' Critical error in background metrics upload (will continue): HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logFeatureVector (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:33123)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.955 [error] 'AugmentExtension' API request 621a1a63-b3bb-45b5-a4ba-77b92d1ecef7 to https://d11.api.augmentcode.com/report-error response 402: Payment Required
2025-07-09 18:08:58.955 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus unknown" due to error: HTTP error: 402 Payment Required
2025-07-09 18:08:58.955 [error] 'ExtensionSessionEventReporter' Error uploading metrics: Error: HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logExtensionSessionEvent (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:35162)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:08:58.955 [error] 'ExtensionSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 402 Payment Required Error: HTTP error: 402 Payment Required
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:22093)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:13012)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.logExtensionSessionEvent (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:35162)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:13:30.455 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-09 18:13:30.455 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-09 18:13:30.455 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false,"enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false}
2025-07-09 18:13:30.455 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-09 18:13:30.455 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-09 18:13:30.455 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-09 18:13:30.455 [info] 'AugmentExtension' Retrieving model config
2025-07-09 18:13:30.971 [error] 'AugmentExtension' API request 8255c827-0d4c-4fc0-b14e-f615d2d93d62 to https://d11.api.augmentcode.com/get-models response 401: Unauthorized
2025-07-09 18:13:30.977 [info] 'activate()' ======== Reloading extension ========
2025-07-09 18:13:31.397 [error] 'AugmentExtension' API request 22247811-2c46-4d70-83f3-98e95ca90bd2 to https://d11.api.augmentcode.com/report-error response 401: Unauthorized
2025-07-09 18:13:31.397 [error] 'AugmentExtension' Dropping error report "get-models call failed with APIStatus unauthenticated" due to error: HTTP error: 401 Unauthorized
2025-07-09 18:13:31.398 [error] 'AugmentExtension' Failed to retrieve model config:  HTTP error: 401 Unauthorized
2025-07-09 18:13:39.159 [info] 'OAuthFlow' Creating new session...
2025-07-09 18:13:39.166 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=uaKJCglAVXxsxfGJnnGnZOqWKEZhidAC4DZ5oBwgeoo&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode-insiders%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=983282d3-91e1-49e8-9bc6-8c9f05fecad4&scope=email&prompt=login
2025-07-09 18:13:57.627 [info] 'activate()' ======== Reloading extension ========
2025-07-09 18:13:57.627 [info] 'AugmentExtension' Retrieving model config
2025-07-09 18:13:57.628 [info] 'OAuthFlow' Created session https://i0.api.augmentcode.com/
2025-07-09 18:13:58.257 [info] 'AugmentExtension' Retrieved model config
2025-07-09 18:13:58.257 [info] 'AugmentExtension' Returning model config
2025-07-09 18:13:58.267 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
  - retryChatStreamTimeouts: false to true
2025-07-09 18:13:58.267 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-07-09 18:13:58.267 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-07-09 18:13:58.267 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-09 18:13:58.267 [info] 'SyncingPermissionTracker' Permission to sync folder /Users/<USER>/ui/HI unknown: no permission information recorded
2025-07-09 18:13:58.267 [info] 'WorkspaceManager' Adding workspace folder HI; folderRoot = /Users/<USER>/ui/HI; syncingPermission = unknown
2025-07-09 18:13:58.332 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-09 18:13:58.332 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-09 18:13:58.332 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-09 18:13:58.334 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-07-09 18:13:58.345 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-09 18:13:58.345 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-09 18:13:58.347 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-09 18:13:58.347 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-09 18:13:58.348 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-09 18:13:58.952 [info] 'DynamicLevelKvStore' Ensuring LevelDB is initialized
2025-07-09 18:13:58.952 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-09 18:13:58.983 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-09 18:13:58.983 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-09 18:13:58.983 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:13:59.000 [error] 'AugmentExtensionSidecar' API request afec02d7-0a2b-46f6-a68c-92bf61d32a65 to https://i0.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-07-09 18:13:59.000 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
	at node:internal/deps/undici/undici:13510:13
2025-07-09 18:13:59.232 [info] 'WorkspaceManager' Beginning full qualification of source folder /Users/<USER>/ui/HI
2025-07-09 18:13:59.239 [info] 'WorkspaceManager' Finished full qualification of source folder /Users/<USER>/ui/HI: trackable files: 0, uploaded fraction: 1, is repo: true
2025-07-09 18:13:59.239 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /Users/<USER>/ui/HI (implicit) at 2025/7/9 18:13:59
2025-07-09 18:13:59.277 [info] 'TaskManager' Setting current root task UUID to 26ca74c6-1a84-43a1-900c-8bd842341504
2025-07-09 18:13:59.284 [info] 'WorkspaceManager[HI]' Start tracking
2025-07-09 18:13:59.285 [info] 'TaskManager' Setting current root task UUID to 26ca74c6-1a84-43a1-900c-8bd842341504
2025-07-09 18:13:59.285 [info] 'TaskManager' Setting current root task UUID to 5afc2d32-349a-4bc6-ae40-073e822aa300
2025-07-09 18:13:59.285 [info] 'TaskManager' Setting current root task UUID to 5afc2d32-349a-4bc6-ae40-073e822aa300
2025-07-09 18:13:59.285 [info] 'TaskManager' Setting current root task UUID to 52626434-5e5a-4ad2-a098-76737bbc474a
2025-07-09 18:13:59.285 [info] 'TaskManager' Setting current root task UUID to 52626434-5e5a-4ad2-a098-76737bbc474a
2025-07-09 18:13:59.286 [info] 'PathMap' Opened source folder /Users/<USER>/ui/HI with id 100
2025-07-09 18:13:59.286 [info] 'OpenFileManager' Opened source folder 100
2025-07-09 18:13:59.286 [info] 'MtimeCache[HI]' reading blob name cache from /Users/<USER>/Library/Application Support/Code - Insiders/User/workspaceStorage/4731722a096173ea0e0c9c3579ccc55b/Augment.vscode-augment/99077ce94897b9d993c4a0253bb363862601318e78358ee81eeae5790bb33bb4/mtime-cache.json
2025-07-09 18:13:59.286 [info] 'MtimeCache[HI]' no blob name cache found at /Users/<USER>/Library/Application Support/Code - Insiders/User/workspaceStorage/4731722a096173ea0e0c9c3579ccc55b/Augment.vscode-augment/99077ce94897b9d993c4a0253bb363862601318e78358ee81eeae5790bb33bb4/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/Users/<USER>/Library/Application Support/Code - Insiders/User/workspaceStorage/4731722a096173ea0e0c9c3579ccc55b/Augment.vscode-augment/99077ce94897b9d993c4a0253bb363862601318e78358ee81eeae5790bb33bb4/mtime-cache.json'
2025-07-09 18:13:59.289 [info] 'WorkspaceManager[HI]' Tracking enabled
2025-07-09 18:13:59.289 [info] 'WorkspaceManager[HI]' Path metrics:
  - directories emitted: 0
  - files emitted: 0
  - other paths emitted: 0
  - total paths emitted: 0
  - timing stats:
    - readDir: 0 ms
    - filter: 0 ms
    - yield: 0 ms
    - total: 0 ms
2025-07-09 18:13:59.289 [info] 'WorkspaceManager[HI]' File metrics:
  - paths accepted: 0
  - paths not accessible: 0
  - not plain files: 0
  - large files: 0
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 0
  - probe batches: 0
  - blob names probed: 0
  - files read: 0
  - blobs uploaded: 0
  - timing stats:
    - ingestPath: 0 ms
    - probe: 0 ms
    - stat: 0 ms
    - read: 0 ms
    - upload: 0 ms
2025-07-09 18:13:59.289 [info] 'WorkspaceManager[HI]' Startup metrics:
  - create SourceFolder: 2 ms
  - read MtimeCache: 0 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 1 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 1 ms
  - purge stale PathMap entries: 0 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 0 ms
  - enable persist: 0 ms
  - total: 4 ms
2025-07-09 18:13:59.289 [info] 'WorkspaceManager' Workspace startup complete in 1028 ms
2025-07-09 18:13:59.632 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1269.12475,"timestamp":"2025-07-09T10:13:59.616Z"}]
2025-07-09 18:13:59.729 [error] 'AugmentExtension' API request 1425725b-802c-479c-bfb5-affdc162fd42 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:13:59.730 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:14:00.077 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:14:00.077 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:14:00.232 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-09 18:14:00.232 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:14:00.232 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:14:00.232 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:14:04.251 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/Users/<USER>/Library/Application Support/Code - Insiders/User/workspaceStorage/4731722a096173ea0e0c9c3579ccc55b/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-2c02d0b8-5f77-40ff-a33e-35c65b2d3b5a.json'
2025-07-09 18:15:35.590 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-09 18:15:35.855 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-09 18:15:35.855 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-09 18:15:35.856 [info] 'ToolsModel' Saved chat mode: CHAT
2025-07-09 18:15:35.856 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-09 18:15:35.882 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-09 18:15:35.882 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-09 18:15:35.882 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:15:35.895 [info] 'TaskManager' Setting current root task UUID to 52626434-5e5a-4ad2-a098-76737bbc474a
2025-07-09 18:15:35.896 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-09 18:15:35.948 [info] 'TaskManager' Setting current root task UUID to b23d5cf4-fb75-4df3-8936-0eb1bdf9eb0d
2025-07-09 18:15:35.950 [info] 'TaskManager' Setting current root task UUID to b23d5cf4-fb75-4df3-8936-0eb1bdf9eb0d
2025-07-09 18:15:35.999 [error] 'AugmentExtensionSidecar' API request 2ce4a489-2b7b-4ace-8013-0c2a786d6f8a to https://i0.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-07-09 18:15:36.000 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
	at node:internal/deps/undici/undici:13510:13
2025-07-09 18:15:36.511 [error] 'AugmentExtension' API request 1763bbed-d2d0-45dd-a53a-53659ab9ba41 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:15:36.511 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:15:36.948 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:15:36.948 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:15:57.274 [info] 'ToolsModel' Tools Mode: AGENT (4 hosts)
2025-07-09 18:15:58.556 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:15:58.556 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:15:58.556 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:15:58.556 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:15:58.623 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":1416.412208,"timestamp":"2025-07-09T10:15:58.559Z"}]
2025-07-09 18:15:59.943 [info] 'ToolsModel' Tools Mode: AGENT (5 hosts)
2025-07-09 18:16:00.985 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:16:16.510 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:16:16.511 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:16:16.511 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:16:16.511 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:16:16.618 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":16752.514459,"timestamp":"2025-07-09T10:16:16.530Z"}]
2025-07-09 18:16:16.678 [info] 'ToolsModel' Tools Mode: AGENT (6 hosts)
2025-07-09 18:16:17.997 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:16:18.109 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:16:19.967 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:16:19.967 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:16:19.967 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:16:19.967 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:16:20.070 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":18878.301958,"timestamp":"2025-07-09T10:16:19.975Z"}]
2025-07-09 18:17:22.915 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:17:24.048 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:17:24.363 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:17:24.363 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:17:25.430 [info] 'ToolsModel' Host: mcpHost (18 tools: 444 enabled, 0 disabled})
 + create_entities_npx
 + modify_entities_npx
 + duplicate_entities_npx
 + reparent_entity_npx
 + delete_entities_npx
 + list_entities_npx
 + add_components_npx
 + remove_components_npx
 + add_script_component_script_npx
 + create_assets_npx
 + list_assets_npx
 + delete_assets_npx
 + instantiate_template_assets_npx
 + set_material_diffuse_npx
 + set_script_text_npx
 + script_parse_npx
 + modify_scene_settings_npx
 + query_scene_settings_npx

2025-07-09 18:17:25.430 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:17:25.430 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:17:25.430 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:17:25.535 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2700.825,"timestamp":"2025-07-09T10:17:25.438Z"}]
2025-07-09 18:17:38.430 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:17:39.741 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:17:40.133 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:17:40.133 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:17:40.962 [info] 'ToolsModel' Host: mcpHost (18 tools: 570 enabled, 0 disabled})
 + create_entities_playcanvas
 + modify_entities_playcanvas
 + duplicate_entities_playcanvas
 + reparent_entity_playcanvas
 + delete_entities_playcanvas
 + list_entities_playcanvas
 + add_components_playcanvas
 + remove_components_playcanvas
 + add_script_component_script_playcanvas
 + create_assets_playcanvas
 + list_assets_playcanvas
 + delete_assets_playcanvas
 + instantiate_template_assets_playcanvas
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas
 + modify_scene_settings_playcanvas
 + query_scene_settings_playcanvas

2025-07-09 18:17:40.962 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:17:40.962 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:17:40.963 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:17:41.069 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2758.463333,"timestamp":"2025-07-09T10:17:40.991Z"}]
2025-07-09 18:19:40.101 [error] 'AugmentExtensionSidecar' API request 7b657e0f-9873-4e6d-9df5-3b3215349c4a to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 18:19:40.612 [error] 'AugmentExtension' API request 1c0025f4-db5c-4853-ab82-8debdc98f706 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:19:40.612 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus invalidArgument" due to error: This operation was aborted
2025-07-09 18:19:40.613 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:19:41.129 [error] 'AugmentExtension' API request d312accd-a353-40f8-b816-146a95a83885 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:19:41.129 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-09 18:19:41.915 [error] 'AugmentExtensionSidecar' API request 0c8d69fd-3e23-4330-b02e-0509160ee941 to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 18:19:42.383 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:20:25.359 [error] 'AugmentExtensionSidecar' API request 0edb2105-91b1-463b-891d-ad8880c5569b to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 18:20:25.867 [error] 'AugmentExtension' API request 7d271743-f4f4-4f3a-a741-fb5a866ac0c2 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:20:25.867 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus invalidArgument" due to error: This operation was aborted
2025-07-09 18:20:25.868 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:20:26.382 [error] 'AugmentExtension' API request 635088ce-e419-4a61-906f-6da107369a60 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:20:26.382 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-09 18:21:02.975 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:21:04.218 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:21:04.412 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:21:04.412 [info] 'ToolsModel' Host: mcpHost (1 tools: 0 enabled, 41 disabled})

 - sequentialthinking_Sequential_thinking
2025-07-09 18:21:05.502 [info] 'ToolsModel' Host: mcpHost (18 tools: 570 enabled, 0 disabled})
 + create_entities_playcanvas
 + modify_entities_playcanvas
 + duplicate_entities_playcanvas
 + reparent_entity_playcanvas
 + delete_entities_playcanvas
 + list_entities_playcanvas
 + add_components_playcanvas
 + remove_components_playcanvas
 + add_script_component_script_playcanvas
 + create_assets_playcanvas
 + list_assets_playcanvas
 + delete_assets_playcanvas
 + instantiate_template_assets_playcanvas
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas
 + modify_scene_settings_playcanvas
 + query_scene_settings_playcanvas

2025-07-09 18:21:05.502 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:21:05.502 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:21:05.502 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:21:05.607 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2747.837375,"timestamp":"2025-07-09T10:21:05.525Z"}]
2025-07-09 18:21:05.703 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:21:06.736 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:21:07.125 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:21:07.125 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:21:08.077 [info] 'ToolsModel' Host: mcpHost (18 tools: 0 enabled, 570 disabled})

 - create_entities_playcanvas
 - modify_entities_playcanvas
 - duplicate_entities_playcanvas
 - reparent_entity_playcanvas
 - delete_entities_playcanvas
 - list_entities_playcanvas
 - add_components_playcanvas
 - remove_components_playcanvas
 - add_script_component_script_playcanvas
 - create_assets_playcanvas
 - list_assets_playcanvas
 - delete_assets_playcanvas
 - instantiate_template_assets_playcanvas
 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
 - modify_scene_settings_playcanvas
 - query_scene_settings_playcanvas
2025-07-09 18:21:08.077 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:21:08.077 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:21:08.077 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:21:08.182 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":3430.39925,"timestamp":"2025-07-09T10:21:08.095Z"},{"name":"set-stored-mcp-servers","durationMs":2844.373666,"timestamp":"2025-07-09T10:21:08.095Z"}]
2025-07-09 18:21:24.043 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:21:25.260 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:21:25.475 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:21:25.475 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:21:26.554 [info] 'ToolsModel' Host: mcpHost (18 tools: 570 enabled, 0 disabled})
 + create_entities_playcanvas
 + modify_entities_playcanvas
 + duplicate_entities_playcanvas
 + reparent_entity_playcanvas
 + delete_entities_playcanvas
 + list_entities_playcanvas
 + add_components_playcanvas
 + remove_components_playcanvas
 + add_script_component_script_playcanvas
 + create_assets_playcanvas
 + list_assets_playcanvas
 + delete_assets_playcanvas
 + instantiate_template_assets_playcanvas
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas
 + modify_scene_settings_playcanvas
 + query_scene_settings_playcanvas

2025-07-09 18:21:26.554 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:21:26.554 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:21:26.554 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:21:26.656 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2745.757042,"timestamp":"2025-07-09T10:21:26.565Z"}]
2025-07-09 18:21:31.507 [error] 'AugmentExtensionSidecar' API request a2a89c00-7b12-4a7e-94f1-7df40e49fc18 to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 18:21:31.982 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:21:49.225 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:21:50.313 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:21:50.633 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:21:50.633 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:21:51.667 [info] 'ToolsModel' Host: mcpHost (18 tools: 0 enabled, 570 disabled})

 - create_entities_playcanvas
 - modify_entities_playcanvas
 - duplicate_entities_playcanvas
 - reparent_entity_playcanvas
 - delete_entities_playcanvas
 - list_entities_playcanvas
 - add_components_playcanvas
 - remove_components_playcanvas
 - add_script_component_script_playcanvas
 - create_assets_playcanvas
 - list_assets_playcanvas
 - delete_assets_playcanvas
 - instantiate_template_assets_playcanvas
 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
 - modify_scene_settings_playcanvas
 - query_scene_settings_playcanvas
2025-07-09 18:21:51.667 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:21:51.667 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:21:51.667 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:21:51.773 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2643.533708,"timestamp":"2025-07-09T10:21:51.676Z"}]
2025-07-09 18:22:47.615 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:22:49.021 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:22:49.293 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:22:49.293 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:22:50.365 [info] 'ToolsModel' Host: mcpHost (18 tools: 570 enabled, 0 disabled})
 + create_entities_playcanvas
 + modify_entities_playcanvas
 + duplicate_entities_playcanvas
 + reparent_entity_playcanvas
 + delete_entities_playcanvas
 + list_entities_playcanvas
 + add_components_playcanvas
 + remove_components_playcanvas
 + add_script_component_script_playcanvas
 + create_assets_playcanvas
 + list_assets_playcanvas
 + delete_assets_playcanvas
 + instantiate_template_assets_playcanvas
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas
 + modify_scene_settings_playcanvas
 + query_scene_settings_playcanvas

2025-07-09 18:22:50.366 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:22:50.366 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:22:50.366 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:22:50.471 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2950.054417,"timestamp":"2025-07-09T10:22:50.373Z"}]
2025-07-09 18:23:23.333 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 18:23:24.519 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 18:23:25.210 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 18:23:25.210 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 18:23:25.794 [info] 'ToolsModel' Host: mcpHost (18 tools: 0 enabled, 570 disabled})

 - create_entities_playcanvas
 - modify_entities_playcanvas
 - duplicate_entities_playcanvas
 - reparent_entity_playcanvas
 - delete_entities_playcanvas
 - list_entities_playcanvas
 - add_components_playcanvas
 - remove_components_playcanvas
 - add_script_component_script_playcanvas
 - create_assets_playcanvas
 - list_assets_playcanvas
 - delete_assets_playcanvas
 - instantiate_template_assets_playcanvas
 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
 - modify_scene_settings_playcanvas
 - query_scene_settings_playcanvas
2025-07-09 18:23:25.794 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 18:23:25.794 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 18:23:25.794 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 18:23:25.897 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2664.071042,"timestamp":"2025-07-09T10:23:25.802Z"}]
2025-07-09 18:23:49.394 [error] 'AugmentExtension' API request ec7aba19-e6ab-406a-919b-c9df6e9bf6ba to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:23:49.395 [error] 'AugmentExtension' Dropping error report "Request timed out: chat-user-message, id: cd66c7c3-c080-4e6c-ba7e-68b9ad21bb7b" due to error: This operation was aborted
2025-07-09 18:30:14.843 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-09 18:30:14.875 [info] 'TaskManager' Setting current root task UUID to c5179b16-9575-4128-b2d9-0eea9b8bf30f
2025-07-09 18:30:14.876 [info] 'TaskManager' Setting current root task UUID to c5179b16-9575-4128-b2d9-0eea9b8bf30f
2025-07-09 18:34:09.195 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19272 msec late.
2025-07-09 18:34:19.925 [error] 'AugmentExtension' API request 4387e3d7-3b6c-4c4c-9241-9392f580bc56 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:34:20.442 [error] 'AugmentExtension' API request 14c003b4-ed70-43ba-baf7-59216d276499 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:34:20.443 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:34:20.443 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:34:20.444 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:34:21.025 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:35:37.172 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19428 msec late.
2025-07-09 18:35:47.796 [error] 'AugmentExtensionSidecar' API request 1199eba7-eff7-4a27-a76c-1dc5f36ec759 to https://i0.api.augmentcode.com/remote-agents/list-stream failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:35:47.797 [error] 'AugmentExtensionSidecar' TypeError: fetch failed
	at node:internal/deps/undici/undici:13510:13
	at processTicksAndRejections (node:internal/process/task_queues:105:5)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processImmediate (node:internal/timers:453:9)
	at async globalThis.fetch (file:///Applications/Visual%20Studio%20Code%20-%20Insiders.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:24322)
	at async av (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28644)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4046)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.getRemoteAgentOverviewsStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:23312)
	at async e.handleRemoteAgentOverviewsStreamRequest (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1798:22423)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:35:47.802 [error] 'AugmentExtension' API request ea662f0a-3dcb-4222-a8d7-4809f1effe2f to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:35:48.321 [error] 'AugmentExtension' API request 72267222-b759-4005-af6a-1611b3d9732b to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:35:48.321 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:35:48.329 [error] 'AugmentExtension' API request 849d4e96-1da4-4462-a49b-d3d40427918c to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:35:48.330 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:35:48.330 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:35:48.331 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:35:49.767 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:37:05.153 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19395 msec late.
2025-07-09 18:37:15.920 [error] 'AugmentExtension' API request d64ef0bb-3138-44b2-ad5c-375317199473 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:37:16.441 [error] 'AugmentExtension' API request 38ef0ed9-b3af-464c-91fb-5ba8a20f4039 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:37:16.441 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:37:16.441 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:37:16.442 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:37:17.733 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:38:33.133 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19365 msec late.
2025-07-09 18:38:43.819 [error] 'AugmentExtension' API request ca05af2b-13a2-445d-b220-dfa5f2583b91 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:38:44.333 [error] 'AugmentExtension' API request 521091c5-8c2c-4cb1-ba3d-8f2c1d2ac91c to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:38:44.333 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:38:44.333 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:38:44.333 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:38:45.546 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:40:01.113 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19433 msec late.
2025-07-09 18:40:11.830 [error] 'AugmentExtensionSidecar' API request 8cea53d9-a338-4dfa-b74f-22bc928bd405 to https://i0.api.augmentcode.com/remote-agents/list-stream failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:40:11.831 [error] 'AugmentExtensionSidecar' TypeError: fetch failed
	at node:internal/deps/undici/undici:13510:13
	at processTicksAndRejections (node:internal/process/task_queues:105:5)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processImmediate (node:internal/timers:453:9)
	at async globalThis.fetch (file:///Applications/Visual%20Studio%20Code%20-%20Insiders.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:24322)
	at async av (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28644)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4046)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.getRemoteAgentOverviewsStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:23312)
	at async e.handleRemoteAgentOverviewsStreamRequest (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1798:22423)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:40:11.836 [error] 'AugmentExtension' API request 520a297f-0474-425e-9757-ad94e8778f43 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:40:12.355 [error] 'AugmentExtension' API request fc302e15-14b3-486c-8ec6-d85e8bd49253 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:40:12.355 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:40:12.376 [error] 'AugmentExtension' API request 64167beb-4f5d-48e8-b2f4-3c011f89ed25 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:40:12.376 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:40:12.376 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at processTicksAndRejections (node:internal/process/task_queues:105:5)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processImmediate (node:internal/timers:453:9)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:40:12.376 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:40:12.985 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:41:29.114 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19441 msec late.
2025-07-09 18:41:39.849 [error] 'AugmentExtension' API request 13cd7c86-c5a7-4cb1-af21-9e80374d317b to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:41:40.375 [error] 'AugmentExtension' API request 06d34721-783b-4aa2-b65e-c5df929e1acf to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:41:40.375 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:41:40.375 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:41:40.375 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:41:41.018 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:43:26.417 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 18624 msec late.
2025-07-09 18:43:30.437 [info] 'AugmentExtension' Retrieving model config
2025-07-09 18:43:37.117 [error] 'AugmentExtension' API request 05217c83-7e8e-4e02-875a-0df7e1d74b42 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:43:37.634 [error] 'AugmentExtension' API request cec8e232-9153-47b6-bc37-1cf45e365495 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:43:37.634 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:43:37.634 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:43:37.635 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:43:38.518 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:43:38.891 [info] 'AugmentExtension' Retrieved model config
2025-07-09 18:43:38.891 [info] 'AugmentExtension' Returning model config
2025-07-09 18:44:54.390 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19446 msec late.
2025-07-09 18:46:22.379 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19298 msec late.
2025-07-09 18:46:32.987 [error] 'AugmentExtension' API request 37972a29-fb84-433d-86ee-b3d6a5930481 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-07-09 18:46:32.996 [error] 'AugmentExtensionSidecar' API request ddd9a920-e591-46fe-87c1-dc726ece2d8b to https://i0.api.augmentcode.com/remote-agents/list-stream failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-07-09 18:46:32.996 [error] 'AugmentExtensionSidecar' TypeError: fetch failed
	at node:internal/deps/undici/undici:13510:13
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async globalThis.fetch (file:///Applications/Visual%20Studio%20Code%20-%20Insiders.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:24322)
	at async av (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28644)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4046)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.getRemoteAgentOverviewsStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:23312)
	at async e.handleRemoteAgentOverviewsStreamRequest (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1798:22423)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 18:46:33.493 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:46:33.494 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:46:34.312 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:48:19.683 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 9775 msec late.
2025-07-09 18:48:25.369 [error] 'AugmentExtension' API request f7bb4e90-078f-4e37-9185-17aa06253fe5 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"errno":-54,"code":"ECONNRESET","syscall":"read"})
2025-07-09 18:48:25.893 [error] 'AugmentExtension' API request 898edb5a-3c72-4ebf-89d8-76db8320149b to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:48:25.893 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:48:25.894 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:48:25.894 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:48:26.866 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:49:47.669 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19389 msec late.
2025-07-09 18:49:58.364 [error] 'AugmentExtension' API request 13b4124a-aa94-4d32-82a5-2d44f3c6ae2b to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:49:58.873 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:49:58.873 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:49:59.424 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:51:15.660 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19473 msec late.
2025-07-09 18:51:26.388 [error] 'AugmentExtension' API request 1149bc17-2ad7-4ed2-89f1-16220d07b9db to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:51:26.903 [error] 'AugmentExtension' API request 97d17d26-c746-4e15-8a34-b4e07c22fa66 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:51:26.903 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:51:26.903 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:51:26.903 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:51:27.508 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:54:40.942 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19461 msec late.
2025-07-09 18:54:51.745 [error] 'AugmentExtension' API request ee97e384-9083-4105-92cf-d39adcdba3a0 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:54:52.264 [error] 'AugmentExtension' API request 76139659-55f1-4bf5-99d7-6853c2656e4d to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:54:52.264 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:54:52.264 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:54:52.264 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:54:53.462 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:56:08.942 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19417 msec late.
2025-07-09 18:56:19.680 [error] 'AugmentExtension' API request 198fa94b-cd94-4d71-a3ca-f6d9a0630f81 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:56:20.182 [error] 'AugmentExtension' API request 9403178a-aee7-40d4-a52d-7952d28ebf74 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:56:20.182 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:56:20.182 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:56:20.183 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:56:20.855 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:56:21.827 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":5095.880291,"timestamp":"2025-07-09T10:56:21.799Z"}]
2025-07-09 18:58:35.590 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 22730 msec late.
2025-07-09 18:58:46.242 [error] 'AugmentExtension' API request 6969bcb3-97a0-4060-af73-ad909197dfc0 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 18:58:46.754 [error] 'AugmentExtension' API request fc0019e8-6806-480a-a4cf-72cbb9e1f42b to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 18:58:46.754 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 18:58:46.754 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 18:58:46.754 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 18:58:48.468 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 18:58:48.605 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":9752.553625,"timestamp":"2025-07-09T10:58:48.581Z"}]
2025-07-09 19:00:03.658 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 3419 msec late.
2025-07-09 19:02:00.865 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 24324 msec late.
2025-07-09 19:02:09.304 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2906.205417,"timestamp":"2025-07-09T11:02:09.277Z"}]
2025-07-09 19:03:28.963 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 14930 msec late.
2025-07-09 19:04:56.828 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19429 msec late.
2025-07-09 19:05:07.567 [error] 'AugmentExtensionSidecar' API request eb7f8d42-a6f8-4629-9817-c7d2a05db03a to https://i0.api.augmentcode.com/remote-agents/list-stream failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 19:05:07.567 [error] 'AugmentExtensionSidecar' TypeError: fetch failed
	at node:internal/deps/undici/undici:13510:13
	at processTicksAndRejections (node:internal/process/task_queues:105:5)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processImmediate (node:internal/timers:453:9)
	at async globalThis.fetch (file:///Applications/Visual%20Studio%20Code%20-%20Insiders.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:24322)
	at async av (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28644)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4046)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.getRemoteAgentOverviewsStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:23312)
	at async e.handleRemoteAgentOverviewsStreamRequest (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1798:22423)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 19:05:07.569 [error] 'AugmentExtension' API request a95918fb-d4b0-4fb5-89eb-b294fe2f19f8 to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 19:05:08.079 [error] 'AugmentExtension' API request 1bb50d70-dd15-47a6-814f-3a65b08d37ed to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:05:08.079 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 19:05:08.092 [error] 'AugmentExtension' API request 678b9456-4e5e-4007-8b25-cd794955ec17 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:05:08.092 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 19:05:08.092 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at processTicksAndRejections (node:internal/process/task_queues:105:5)
	at runNextTicks (node:internal/process/task_queues:69:3)
	at process.processImmediate (node:internal/timers:453:9)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 19:05:08.093 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 19:05:08.770 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 19:06:24.819 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 19370 msec late.
2025-07-09 19:06:35.629 [error] 'AugmentExtension' API request b285cc69-469f-42e8-a4ba-ceaab85e97cc to https://i0.api.augmentcode.com/client-metrics failed: fetch failed (due to {"name":"ConnectTimeoutError","code":"UND_ERR_CONNECT_TIMEOUT"})
2025-07-09 19:06:36.152 [error] 'AugmentExtension' API request 22e8205a-ca28-4ba6-81b7-84ae6cd8b3ff to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:06:36.152 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-09 19:06:36.152 [error] 'ClientMetricsReporter' Error uploading metrics: Error: fetch failed Error: fetch failed
	at e.transientIssue (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21661)
	at BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:12548)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApi (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:55996)
	at async BQ.clientMetrics (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:51977)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:30028
	at async eo (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:28240)
	at async e._doUpload (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29927)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:29250
2025-07-09 19:06:36.153 [info] 'ClientMetricsReporter' Operation failed with error Error: fetch failed, retrying in 100 ms; retries = 0
2025-07-09 19:06:36.923 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-09 19:07:00.414 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1185.909666,"timestamp":"2025-07-09T11:07:00.353Z"}]
2025-07-09 19:13:30.440 [info] 'AugmentExtension' Retrieving model config
2025-07-09 19:13:31.950 [info] 'AugmentExtension' Retrieved model config
2025-07-09 19:13:31.950 [info] 'AugmentExtension' Returning model config
2025-07-09 19:15:14.891 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-07-09 19:15:14.928 [info] 'TaskManager' Setting current root task UUID to 057e9373-d056-4a5b-bb47-609c8843a58b
2025-07-09 19:15:14.928 [info] 'TaskManager' Setting current root task UUID to 057e9373-d056-4a5b-bb47-609c8843a58b
2025-07-09 19:15:19.023 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:15:20.306 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:15:20.634 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:15:20.635 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:15:21.651 [info] 'ToolsModel' Host: mcpHost (16 tools: 512 enabled, 0 disabled})
 + modify_entities_playcanvas
 + duplicate_entities_playcanvas
 + reparent_entity_playcanvas
 + delete_entities_playcanvas
 + list_entities_playcanvas
 + add_components_playcanvas
 + remove_components_playcanvas
 + add_script_component_script_playcanvas
 + list_assets_playcanvas
 + delete_assets_playcanvas
 + instantiate_template_assets_playcanvas
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas
 + modify_scene_settings_playcanvas
 + query_scene_settings_playcanvas

2025-07-09 19:15:21.651 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:15:21.651 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:15:21.651 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:15:21.756 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2825.498583,"timestamp":"2025-07-09T11:15:21.669Z"}]
2025-07-09 19:15:30.363 [error] 'AugmentExtensionSidecar' API request 535ffceb-2863-489a-a3d0-8053f087372e to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 19:15:30.871 [error] 'AugmentExtension' API request ca8dc384-16e4-4913-bb5d-2ed78512eb60 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:15:30.871 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus invalidArgument" due to error: This operation was aborted
2025-07-09 19:15:30.872 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 19:15:31.387 [error] 'AugmentExtension' API request 3db992bb-6678-454e-9e0d-13cf2ebe9a8d to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:15:31.387 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-09 19:15:32.752 [error] 'AugmentExtensionSidecar' API request 90d749cd-5ced-4d83-b853-58bdf2ed0884 to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 19:15:33.260 [error] 'AugmentExtension' API request 172587e3-bfc6-4a39-98fe-81310afa2b5d to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:15:33.261 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus invalidArgument" due to error: This operation was aborted
2025-07-09 19:15:33.261 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 19:15:33.780 [error] 'AugmentExtension' API request 1b72d9dc-adcf-4245-b0c9-62df16777889 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:15:33.780 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-09 19:16:52.462 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:16:52.691 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:16:53.838 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:16:54.125 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:16:54.125 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:16:55.053 [info] 'ToolsModel' Host: mcpHost (4 tools: 0 enabled, 133 disabled})

 - add_script_component_script_playcanvas
 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
2025-07-09 19:16:55.053 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:16:55.055 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:16:55.055 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:16:55.088 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":3855.848583,"timestamp":"2025-07-09T11:16:55.062Z"}]
2025-07-09 19:16:56.198 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:16:56.672 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:16:56.672 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:16:57.627 [info] 'ToolsModel' Host: mcpHost (4 tools: 133 enabled, 0 disabled})
 + add_script_component_script_playcanvas
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas

2025-07-09 19:16:57.627 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:16:57.627 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:16:57.627 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:16:57.713 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":6201.554709,"timestamp":"2025-07-09T11:16:57.633Z"}]
2025-07-09 19:17:41.747 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:17:43.087 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:17:43.344 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:17:43.344 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:17:44.318 [info] 'ToolsModel' Host: mcpHost (3 tools: 0 enabled, 91 disabled})

 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
2025-07-09 19:17:44.318 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:17:44.318 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:17:44.318 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:17:44.377 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2830.354334,"timestamp":"2025-07-09T11:17:44.324Z"}]
2025-07-09 19:17:44.499 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:17:45.980 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:17:46.212 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:17:46.212 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:17:47.209 [info] 'ToolsModel' Host: mcpHost (3 tools: 91 enabled, 0 disabled})
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas

2025-07-09 19:17:47.209 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:17:47.209 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:17:47.210 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:17:47.279 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":5319.372125,"timestamp":"2025-07-09T11:17:47.215Z"}]
2025-07-09 19:18:24.967 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:18:26.586 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:18:26.690 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:18:26.690 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:18:27.645 [info] 'ToolsModel' Host: mcpHost (1 tools: 0 enabled, 26 disabled})

 - script_parse_playcanvas
2025-07-09 19:18:27.645 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:18:27.645 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:18:27.645 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:18:27.750 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2915.275083,"timestamp":"2025-07-09T11:18:27.652Z"}]
2025-07-09 19:18:27.849 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:18:29.222 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:18:29.469 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:18:29.469 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:18:30.441 [info] 'ToolsModel' Host: mcpHost (1 tools: 26 enabled, 0 disabled})
 + script_parse_playcanvas

2025-07-09 19:18:30.441 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:18:30.441 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:18:30.441 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:18:30.539 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":5429.859083,"timestamp":"2025-07-09T11:18:30.446Z"}]
2025-07-09 19:18:52.195 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:18:53.639 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:18:54.070 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:18:54.071 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:18:54.865 [info] 'ToolsModel' Host: mcpHost (2 tools: 0 enabled, 56 disabled})

 - set_script_text_playcanvas
 - script_parse_playcanvas
2025-07-09 19:18:54.865 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:18:54.865 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:18:54.865 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:18:54.908 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2923.104083,"timestamp":"2025-07-09T11:18:54.873Z"}]
2025-07-09 19:18:55.042 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:18:56.209 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:18:56.477 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:18:56.477 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:18:57.392 [info] 'ToolsModel' Host: mcpHost (2 tools: 56 enabled, 0 disabled})
 + set_script_text_playcanvas
 + script_parse_playcanvas

2025-07-09 19:18:57.392 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:18:57.392 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:18:57.392 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:18:57.416 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":4968.826625,"timestamp":"2025-07-09T11:18:57.397Z"}]
2025-07-09 19:19:14.773 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:19:16.186 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:19:16.460 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:19:16.460 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:19:17.401 [info] 'ToolsModel' Host: mcpHost (3 tools: 0 enabled, 91 disabled})

 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
2025-07-09 19:19:17.401 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:19:17.401 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:19:17.401 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:19:17.490 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2867.048958,"timestamp":"2025-07-09T11:19:17.408Z"}]
2025-07-09 19:19:17.576 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:19:18.721 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:19:19.136 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:19:19.136 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:19:20.063 [info] 'ToolsModel' Host: mcpHost (3 tools: 91 enabled, 0 disabled})
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas

2025-07-09 19:19:20.063 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:19:20.063 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:19:20.063 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:19:20.077 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":5024.783416,"timestamp":"2025-07-09T11:19:20.069Z"}]
2025-07-09 19:19:28.623 [error] 'AugmentExtensionSidecar' API request 5b33a416-448c-4880-8ff2-aa0bc820828c to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 19:19:29.132 [error] 'AugmentExtension' API request b2dfab1c-3ebd-43c7-aa49-6ca7a2086bef to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:19:29.132 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus invalidArgument" due to error: This operation was aborted
2025-07-09 19:19:29.133 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 19:19:29.651 [error] 'AugmentExtension' API request f18037b1-6b6d-4d88-812a-1a7acfa48f0f to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:19:29.651 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-09 19:20:48.096 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:20:49.410 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:20:49.622 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:20:49.622 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:20:50.584 [info] 'ToolsModel' Host: mcpHost (3 tools: 0 enabled, 91 disabled})

 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
2025-07-09 19:20:50.584 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:20:50.584 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:20:50.584 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:20:50.693 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2720.058083,"timestamp":"2025-07-09T11:20:50.590Z"}]
2025-07-09 19:20:50.766 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:20:51.872 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:20:52.118 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:20:52.119 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:20:53.143 [info] 'ToolsModel' Host: mcpHost (3 tools: 91 enabled, 0 disabled})
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas

2025-07-09 19:20:53.143 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:20:53.143 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:20:53.143 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:20:53.159 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":4721.227666,"timestamp":"2025-07-09T11:20:53.149Z"}]
2025-07-09 19:20:58.773 [error] 'AugmentExtensionSidecar' API request fd5396b5-440b-4c0f-aa0b-3875e76ef15b to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 19:20:59.284 [error] 'AugmentExtension' API request 696612e1-49f3-42f2-b134-8b04b94dc779 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:20:59.284 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus invalidArgument" due to error: This operation was aborted
2025-07-09 19:20:59.284 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
2025-07-09 19:20:59.795 [error] 'AugmentExtension' API request 6bd8d51d-9dfc-446b-b193-b70820d9e674 to https://i0.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-09 19:20:59.796 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-09 19:24:09.717 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:24:10.935 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:24:11.239 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:24:11.239 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:24:12.134 [info] 'ToolsModel' Host: mcpHost (3 tools: 0 enabled, 91 disabled})

 - set_material_diffuse_playcanvas
 - set_script_text_playcanvas
 - script_parse_playcanvas
2025-07-09 19:24:12.134 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:24:12.134 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:24:12.134 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:24:12.202 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2632.075125,"timestamp":"2025-07-09T11:24:12.140Z"}]
2025-07-09 19:24:14.918 [info] 'ToolsModel' Tools Mode: AGENT (7 hosts)
2025-07-09 19:24:16.141 [info] 'ToolsModel' Host: mcpHost (2 tools: 61 enabled, 0 disabled})
 + resolve-library-id_Context_7
 + get-library-docs_Context_7

2025-07-09 19:24:16.300 [info] 'ToolsModel' Host: mcpHost (25 tools: 822 enabled, 0 disabled})
 + browser_close_Playwright
 + browser_resize_Playwright
 + browser_console_messages_Playwright
 + browser_handle_dialog_Playwright
 + browser_file_upload_Playwright
 + browser_install_Playwright
 + browser_press_key_Playwright
 + browser_navigate_Playwright
 + browser_navigate_back_Playwright
 + browser_navigate_forward_Playwright
 + browser_network_requests_Playwright
 + browser_pdf_save_Playwright
 + browser_take_screenshot_Playwright
 + browser_snapshot_Playwright
 + browser_click_Playwright
 + browser_drag_Playwright
 + browser_hover_Playwright
 + browser_type_Playwright
 + browser_select_option_Playwright
 + browser_tab_list_Playwright
 + browser_tab_new_Playwright
 + browser_tab_select_Playwright
 + browser_tab_close_Playwright
 + browser_generate_playwright_test_Playwright
 + browser_wait_for_Playwright

2025-07-09 19:24:16.301 [info] 'ToolsModel' Host: mcpHost (1 tools: 41 enabled, 0 disabled})
 + sequentialthinking_Sequential_thinking

2025-07-09 19:24:17.262 [info] 'ToolsModel' Host: mcpHost (3 tools: 91 enabled, 0 disabled})
 + set_material_diffuse_playcanvas
 + set_script_text_playcanvas
 + script_parse_playcanvas

2025-07-09 19:24:17.263 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-09 19:24:17.263 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-09 19:24:17.263 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-09 19:24:17.288 [info] 'StallDetector' Recent work: [{"name":"set-stored-mcp-servers","durationMs":2545.823458,"timestamp":"2025-07-09T11:24:17.267Z"}]
2025-07-09 19:24:23.316 [error] 'AugmentExtensionSidecar' API request 3a62e8d2-4c3b-4e38-a311-ab1ca6fd58c4 to https://i0.api.augmentcode.com/chat-stream response 400: Bad Request
2025-07-09 19:24:23.819 [error] 'ChatApp' Chat stream failed: Error: HTTP error: 400 Bad Request
Error: HTTP error: 400 Bad Request
	at e.fromResponse (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:310:21955)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:539:4715)
	at async BQ.callApiStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:56825)
	at async BQ.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:543:17896)
	at async e.startChatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:35718)
	at async e.chatStream (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:744:33884)
	at async Ok.onUserSendMessage (/Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:1830:3111)
	at async /Users/<USER>/.vscode-insiders/extensions/augment.vscode-augment-0.498.0/out/extension.js:546:5064
